#!/usr/bin/env node

/**
 * 生成所有 Heroicons 的 safelist 配置
 * 确保所有图标都被 Tailwind CSS 编译
 */

const fs = require('fs');
const path = require('path');

function generateHeroiconsSafelist() {
  const iconsDir = path.join(__dirname, '../deps/heroicons/optimized');
  const safelist = [];
  
  const icons = [
    ['', '/24/outline'],
    ['-solid', '/24/solid'],
    ['-mini', '/20/solid'],
    ['-micro', '/16/solid']
  ];
  
  console.log('🔍 扫描 Heroicons 目录...');
  
  icons.forEach(([suffix, dir]) => {
    const dirPath = path.join(iconsDir, dir);
    
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      console.log(`📁 处理目录: ${dir} (${files.length} 个文件)`);
      
      files.forEach(file => {
        if (file.endsWith('.svg')) {
          const name = path.basename(file, '.svg') + suffix;
          safelist.push(`hero-${name}`);
        }
      });
    } else {
      console.warn(`⚠️  目录不存在: ${dirPath}`);
    }
  });
  
  console.log(`✅ 生成了 ${safelist.length} 个图标类名`);
  
  // 生成 JavaScript 配置
  const configContent = `// 自动生成的 Heroicons safelist 配置
// 运行 node scripts/generate_heroicons_safelist.js 重新生成

module.exports = [
  // 所有 Heroicons 图标类
${safelist.map(icon => `  '${icon}',`).join('\n')}
  
  // 尺寸类
  'size-4', 'size-5', 'size-6', 'size-8',
  
  // 颜色类
  'text-primary', 'text-secondary', 'text-accent', 'text-neutral',
  'text-info', 'text-success', 'text-warning', 'text-error',
  'text-red-500', 'text-green-500', 'text-blue-500', 'text-yellow-500',
  'text-purple-500', 'text-pink-500', 'text-indigo-500', 'text-gray-500',
];
`;
  
  // 写入配置文件
  const outputPath = path.join(__dirname, '../assets/heroicons-safelist.js');
  fs.writeFileSync(outputPath, configContent);
  console.log(`📝 配置已写入: ${outputPath}`);
  
  // 生成统计信息
  const stats = {
    total: safelist.length,
    outline: safelist.filter(name => !name.includes('-solid') && !name.includes('-mini') && !name.includes('-micro')).length,
    solid: safelist.filter(name => name.includes('-solid')).length,
    mini: safelist.filter(name => name.includes('-mini')).length,
    micro: safelist.filter(name => name.includes('-micro')).length
  };
  
  console.log('\n📊 统计信息:');
  console.log(`   总计: ${stats.total} 个图标`);
  console.log(`   Outline (24px): ${stats.outline} 个`);
  console.log(`   Solid (24px): ${stats.solid} 个`);
  console.log(`   Mini (20px): ${stats.mini} 个`);
  console.log(`   Micro (16px): ${stats.micro} 个`);
  
  return safelist;
}

// 如果直接运行此脚本
if (require.main === module) {
  generateHeroiconsSafelist();
}

module.exports = generateHeroiconsSafelist;
