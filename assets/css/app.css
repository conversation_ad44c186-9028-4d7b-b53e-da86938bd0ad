/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../../deps/cinder";
@source "../../deps/ash_authentication_phoenix";
@source "../../deps/backpex/**/*.*ex";
@source "../../deps/backpex/assets/js/**/*.*js";

@source "../css";
@source "../js";
@source "../../lib/cypridina_web";

/* 引入优化后的样式 */
@import "./optimized-styles.css";
@import "./unified-page-styles.css";
/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
      The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";

/* 强制包含所有 Heroicons 图标类 */
@import "./heroicons-force.css";

/* daisyUI Tailwind Plugin. You can update this file by fetching the latest version with:
      curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui.js
      Make sure to look at the daisyUI changelog: https://daisyui.com/docs/changelog/ */
@plugin "../vendor/daisyui" {
  /* themes: false; */
  themes: light --default, dark --prefersdark, cupcake, cyberpunk;
}

/* daisyUI theme plugin. You can update this file by fetching the latest version with:
     curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui-theme.js
     We ship with two themes, a light one inspired on Phoenix colors and a dark one inspired
     on Elixir colors. Build your own at: https://daisyui.com/theme-generator/ */
@plugin "../vendor/daisyui-theme" {
  name: "dark";
  default: false;
  prefersdark: true;
  color-scheme: "dark";
  --color-base-100: oklch(30.33% 0.016 252.42);
  --color-base-200: oklch(25.26% 0.014 253.1);
  --color-base-300: oklch(20.15% 0.012 254.09);
  --color-base-content: oklch(97.807% 0.029 256.847);
  --color-primary: oklch(58% 0.233 277.117);
  --color-primary-content: oklch(96% 0.018 272.314);
  --color-secondary: oklch(58% 0.233 277.117);
  --color-secondary-content: oklch(96% 0.018 272.314);
  --color-accent: oklch(60% 0.25 292.717);
  --color-accent-content: oklch(96% 0.016 293.756);
  --color-neutral: oklch(37% 0.044 257.287);
  --color-neutral-content: oklch(98% 0.003 247.858);
  --color-info: oklch(58% 0.158 241.966);
  --color-info-content: oklch(97% 0.013 236.62);
  --color-success: oklch(60% 0.118 184.704);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(58% 0.253 17.585);
  --color-error-content: oklch(96% 0.015 12.422);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

@plugin "../vendor/daisyui-theme" {
  name: "light";
  default: true;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(98% 0 0);
  --color-base-200: oklch(96% 0.001 286.375);
  --color-base-300: oklch(92% 0.004 286.32);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(70% 0.213 47.604);
  --color-primary-content: oklch(98% 0.016 73.684);
  --color-secondary: oklch(55% 0.027 264.364);
  --color-secondary-content: oklch(98% 0.002 247.839);
  --color-accent: oklch(0% 0 0);
  --color-accent-content: oklch(100% 0 0);
  --color-neutral: oklch(44% 0.017 285.786);
  --color-neutral-content: oklch(98% 0 0);
  --color-info: oklch(62% 0.214 259.815);
  --color-info-content: oklch(97% 0.014 254.604);
  --color-success: oklch(70% 0.14 182.503);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(66% 0.179 58.318);
  --color-warning-content: oklch(98% 0.022 95.277);
  --color-error: oklch(65% 0.241 354.308);
  --color-error-content: oklch(97% 0.014 343.198);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.21875rem;
  --size-field: 0.21875rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;
}

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@custom-variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@custom-variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

@layer base {

  /* CSS Reset - 移除重复的基础样式 */
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }

  html,
  :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  body {
    line-height: inherit;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b,
  strong {
    font-weight: bolder;
  }

  code,
  kbd,
  samp,
  pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol,
  ul,
  menu {
    list-style: none;
  }

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    vertical-align: middle;
  }

  img,
  video {
    max-width: 100%;
    height: auto;
  }

  button,
  input,
  select,
  optgroup,
  textarea,
  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit,
  ::-webkit-datetime-edit-year-field,
  ::-webkit-datetime-edit-month-field,
  ::-webkit-datetime-edit-day-field,
  ::-webkit-datetime-edit-hour-field,
  ::-webkit-datetime-edit-minute-field,
  ::-webkit-datetime-edit-second-field,
  ::-webkit-datetime-edit-millisecond-field,
  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button,
  input:where([type='button'], [type='reset'], [type='submit']),
  ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden='until-found'])) {
    display: none !important;
  }
}

/* Make LiveView wrapper divs transparent for layout */
[data-phx-session] {
  display: contents
}

/* This file is for your main application CSS */

/* ===== CSS 变量定义 ===== */
:root {
  /* 通用动画时长 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 通用阴影 */
  --shadow-sm: 0 2px 4px oklch(var(--color-base-content) / 0.1);
  --shadow-md: 0 4px 8px oklch(var(--color-base-content) / 0.1);
  --shadow-lg: 0 8px 16px oklch(var(--color-base-content) / 0.15);
  --shadow-xl: 0 12px 24px oklch(var(--color-base-content) / 0.2);

  /* 通用渐变 */
  --gradient-primary: linear-gradient(135deg, oklch(var(--color-primary)) 0%, oklch(var(--color-secondary)) 100%);
  --gradient-surface: linear-gradient(135deg, oklch(var(--color-base-100)) 0%, oklch(var(--color-base-200)) 100%);
  --gradient-accent: linear-gradient(90deg, oklch(var(--color-primary)) 0%, oklch(var(--color-secondary)) 50%, oklch(var(--color-accent)) 100%);

  /* 通用边框半径 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* 毛玻璃效果 */
  --backdrop-blur: blur(20px);
  --backdrop-blur-sm: blur(10px);
}

/* ===== 通用工具类 ===== */
.glass-effect {
  backdrop-filter: var(--backdrop-blur);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass-effect {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.transition-smooth {
  transition: all var(--transition-normal) ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.gradient-text {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ===== 通用动画 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes glow {

  0%,
  100% {
    box-shadow: 0 0 0 0 oklch(var(--color-primary) / 0.7);
  }

  50% {
    box-shadow: 0 0 0 4px oklch(var(--color-primary) / 0);
  }
}

/* ===== 管理后台布局优化样式 ===== */
.admin-topbar {
  backdrop-filter: var(--backdrop-blur-sm);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal) ease;
}

[data-theme="dark"] .admin-topbar {
  background: rgba(0, 0, 0, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== 侧边栏样式 ===== */
.admin-sidebar {
  background: var(--gradient-surface);
  border-right: 1px solid oklch(var(--color-base-300));
}

/* 侧边栏菜单项 - 统一悬停效果 */
.sidebar-item-enhanced,
.game-sidebar-item {
  transition: all var(--transition-fast) ease;
  border-radius: var(--radius-md);
  margin: 0.125rem 0.5rem;
  position: relative;
  overflow: hidden;
}

.sidebar-item-enhanced:hover,
.game-sidebar-item:hover {
  background: oklch(var(--color-primary) / 0.1);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

/* 侧边栏分组标题样式 */
.sidebar-section-title {
  position: relative;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
}

.sidebar-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1rem;
  right: 1rem;
  height: 1px;
  background: linear-gradient(90deg,
      oklch(var(--color-primary) / 0.3) 0%,
      transparent 100%);
}

/* 子菜单缩进线条 */
.sidebar-submenu-line {
  position: relative;
}

.sidebar-submenu-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg,
      oklch(var(--color-base-300)) 0%,
      oklch(var(--color-primary) / 0.3) 50%,
      oklch(var(--color-base-300)) 100%);
  border-radius: 1px;
}

/* ===== 通知和状态指示器 ===== */
.notification-badge,
.status-indicator::after {
  animation: glow 2s ease-in-out infinite;
}

/* 搜索框增强 */
.admin-search-input {
  transition: all 0.3s ease;
}

.admin-search-input:focus {
  box-shadow: 0 0 0 3px oklch(var(--color-primary) / 0.2);
  transform: scale(1.02);
}

/* 用户头像增强 */
.user-avatar-enhanced {
  transition: all 0.3s ease;
  position: relative;
}

.user-avatar-enhanced:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

.user-avatar-enhanced::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg,
      oklch(var(--color-primary)),
      oklch(var(--color-secondary)));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-avatar-enhanced:hover::after {
  opacity: 1;
}

/* 主内容区域渐变背景 */
.admin-main-content {
  background: linear-gradient(135deg,
      oklch(var(--color-base-100)) 0%,
      oklch(var(--color-base-200)) 50%,
      oklch(var(--color-base-100)) 100%);
  min-height: 100vh;
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: oklch(var(--color-success));
  border: 2px solid oklch(var(--color-base-100));
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 仪表盘专用样式 */

/* ===== 卡片样式 - 统一设计 ===== */
.stat-card,
.game-card,
.room-config-card {
  background: oklch(var(--color-base-100));
  border: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
}

.stat-card::before,
.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.stat-card:hover,
.game-card:hover,
.room-config-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card:hover::before,
.game-card:hover::before {
  opacity: 1;
}

/* ===== 文本和数值样式 ===== */
.stat-value,
.jackpot-balance,
.metric-value {
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 3s ease-in-out infinite;
}

.jackpot-balance {
  font-size: 1.5rem;
}

.metric-value {
  font-size: 1.125rem;
}

/* ===== 容器样式 ===== */
.chart-container,
.game-stats-chart,
.monitoring-panel {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background: var(--gradient-surface);
  border: 1px solid oklch(var(--color-base-300));
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16rem;
  color: oklch(var(--color-base-content) / 0.4);
  background: repeating-linear-gradient(45deg,
      transparent,
      transparent 10px,
      oklch(var(--color-base-300) / 0.1) 10px,
      oklch(var(--color-base-300) / 0.1) 20px);
}

/* ===== Backpex 用户页面动态布局样式 ===== */
/* 确保 Backpex 主容器使用全部可用高度 */
.backpex-live-resource,
[data-backpex-resource],
.backpex-resource-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Backpex 内容区域样式 */
.backpex-content,
.backpex-live-resource>div,
[data-backpex-resource]>div {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* Backpex 表格容器样式 */
.backpex-table-container,
.table-container,
.overflow-x-auto {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: oklch(var(--color-base-100));
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  margin: 0.5rem 0;
}

/* Backpex 表格样式 */
.backpex-table,
.table {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

/* Backpex 分页器样式 */
.backpex-pagination,
.pagination {
  flex-shrink: 0;
  padding: 1rem;
  border-top: 1px solid oklch(var(--color-base-300));
  background: oklch(var(--color-base-50));
}

/* Backpex 头部操作区域 */
.backpex-header,
.resource-header {
  flex-shrink: 0;
  padding: 1.5rem;
  background: oklch(var(--color-base-100));
  border-bottom: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

/* Backpex 表单容器样式 */
.backpex-form-container,
.form-container {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
  min-height: 0;
}

/* 通用 Backpex 组件高度调整 */
.backpex-live-resource .card,
.backpex-live-resource .bg-base-100 {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.backpex-live-resource .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {

  .backpex-header,
  .resource-header {
    padding: 1rem;
  }

  .backpex-form-container,
  .form-container {
    padding: 1rem;
  }

  .backpex-pagination,
  .pagination {
    padding: 0.75rem;
  }
}

/* 确保主要内容区域使用全部高度 */
main[data-backpex-resource] {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 修复可能的滚动问题 */
.backpex-live-resource .overflow-auto {
  flex: 1;
  min-height: 0;
}

/* ===== 全局 Backpex 动态布局样式 ===== */
/* 确保所有 LiveView 容器使用全部高度 */
[data-phx-main] {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Backpex 主要内容区域 */
[data-phx-main]>div {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 表格容器自动调整 */
.overflow-x-auto:has(.table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: oklch(var(--color-base-100));
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.overflow-x-auto .table {
  flex: 1;
  min-height: 0;
}

/* 分页器固定在底部 */
.join {
  flex-shrink: 0;
  margin-top: auto;
  padding: 1rem;
  background: oklch(var(--color-base-50));
  border-top: 1px solid oklch(var(--color-base-300));
  border-radius: 0 0 0.75rem 0.75rem;
}

/* 搜索和过滤器区域 */
.space-y-4>div:first-child {
  flex-shrink: 0;
}

/* 确保卡片内容区域使用全部高度 */
.card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* ===== 自定义用户管理页面样式 ===== */
/* 用户管理页面表格头部固定 */
.table thead th {
  position: sticky;
  top: 0;
  background: oklch(var(--color-base-100));
  z-index: 10;
  border-bottom: 2px solid oklch(var(--color-base-300));
}

/* 表格行悬停效果 */
.table tbody tr:hover {
  background: oklch(var(--color-base-200) / 0.5);
  transform: scale(1.001);
  transition: all 0.2s ease;
}

/* 分页区域样式 */
.join {
  flex-shrink: 0;
}

/* 空状态居中显示 */
.flex-1.flex.items-center.justify-center {
  min-height: 200px;
}

/* 搜索区域优化 */
.input-group .input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

/* 模态框优化 */
.modal-box {
  max-height: 90vh;
  overflow-y: auto;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
  }

  .btn-xs {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* ===== 列表项样式 ===== */
.activity-item,
.monitoring-metric {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  border-left: 3px solid transparent;
  transition: all var(--transition-fast) ease;
}

.activity-item:hover,
.monitoring-metric:hover {
  background: oklch(var(--color-base-200));
  border-left-color: oklch(var(--color-primary));
  transform: translateX(4px);
}

/* 快速操作按钮增强 */
.quick-action-btn {
  @apply btn btn-outline transition-all duration-300 relative overflow-hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      oklch(var(--color-primary) / 0.2) 50%,
      transparent 100%);
  transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

/* 系统状态警告样式 */
.system-alert {
  @apply alert transition-all duration-300;
  border-left: 4px solid currentColor;
}

.system-alert:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px oklch(var(--color-base-content) / 0.1);
}

/* 收入分析卡片 */
.revenue-stat {
  @apply stat bg-gradient-to-br from-base-200 to-base-300 rounded-lg p-4 hover:shadow-md transition-all duration-300;
  position: relative;
}

.revenue-stat::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 20px 20px 0;
  border-color: transparent oklch(var(--color-primary) / 0.1) transparent transparent;
  transition: all 0.3s ease;
}

.revenue-stat:hover::after {
  border-width: 0 30px 30px 0;
}

/* 表格增强样式 */
.dashboard-table {
  @apply table table-zebra table-sm;
}

.dashboard-table th {
  @apply bg-base-200 text-base-content font-semibold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.dashboard-table tr:hover {
  @apply bg-primary/5;
  transform: scale(1.01);
}

/* 加载动画增强 */
.loading-enhanced {
  @apply loading loading-spinner loading-lg;
  background: linear-gradient(45deg,
      oklch(var(--color-primary)),
      oklch(var(--color-secondary)));
  border-radius: 50%;
  animation: loading-pulse 2s ease-in-out infinite;
}

@keyframes loading-pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stat-card {
    @apply p-4;
  }

  .stat-value {
    @apply text-xl;
  }

  .quick-action-btn {
    @apply btn-sm;
  }

  .chart-container {
    height: 200px;
  }
}

/* 游戏管理侧边栏样式 */
.game-management-sidebar {
  @apply flex flex-col;
  width: 280px;
  min-height: 100vh;
  background: linear-gradient(180deg,
      oklch(var(--color-base-100)) 0%,
      oklch(var(--color-base-200)) 100%);
  box-shadow: 2px 0 8px oklch(var(--color-base-content) / 0.1);
}

.game-sidebar-item {
  @apply relative overflow-hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      oklch(var(--color-primary) / 0.1) 50%,
      transparent 100%);
  transition: left 0.5s ease;
  z-index: 0;
}

.game-sidebar-item:hover::before {
  left: 100%;
}

.game-sidebar-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px oklch(var(--color-primary) / 0.2);
}

.game-sidebar-item>* {
  position: relative;
  z-index: 1;
}

/* 游戏管理页面布局 */
.game-management-layout {
  @apply flex min-h-screen;
}

.game-management-content {
  @apply flex-1 bg-gradient-to-br from-base-100 to-base-200;
  min-height: 100vh;
}

/* 游戏卡片样式 */
.game-card {
  @apply bg-base-100 rounded-xl shadow-lg border border-base-300 overflow-hidden transition-all duration-300;
  position: relative;
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
      oklch(var(--color-primary)) 0%,
      oklch(var(--color-secondary)) 50%,
      oklch(var(--color-accent)) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.game-card:hover::before {
  opacity: 1;
}

.game-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px oklch(var(--color-base-content) / 0.15);
}

/* 游戏状态指示器 */
.game-status-indicator {
  @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
}

.game-status-active {
  @apply bg-success/10 text-success border border-success/20;
}

.game-status-inactive {
  @apply bg-neutral/10 text-neutral border border-neutral/20;
}

.game-status-maintenance {
  @apply bg-warning/10 text-warning border border-warning/20;
}

.game-status-error {
  @apply bg-error/10 text-error border border-error/20;
}

/* 奖池监控样式 */
.jackpot-monitor {
  @apply bg-gradient-to-r from-warning/5 to-success/5 border border-warning/20 rounded-lg p-4;
}

.jackpot-balance {
  @apply text-2xl font-bold bg-gradient-to-r from-warning to-success bg-clip-text text-transparent;
}

.jackpot-health-good {
  @apply text-success;
}

.jackpot-health-warning {
  @apply text-warning;
}

.jackpot-health-critical {
  @apply text-error;
}

/* 房间配置样式 */
.room-config-card {
  @apply bg-base-100 border border-base-300 rounded-lg p-4 hover:shadow-md transition-all duration-200;
}

.room-config-card:hover {
  @apply border-primary/30;
  transform: scale(1.02);
}

/* 游戏统计图表样式 */
.game-stats-chart {
  @apply bg-base-100 rounded-lg border border-base-300 p-6;
  background: linear-gradient(135deg,
      oklch(var(--color-base-100)) 0%,
      oklch(var(--color-base-200)) 100%);
}

/* 实时监控面板 */
.monitoring-panel {
  @apply bg-base-100 border border-base-300 rounded-lg overflow-hidden;
  background: linear-gradient(135deg,
      oklch(var(--color-base-100)) 0%,
      oklch(var(--color-base-200)) 100%);
}

.monitoring-metric {
  @apply flex items-center justify-between p-4 border-b border-base-300 last:border-b-0;
  transition: background-color 0.2s ease;
}

.monitoring-metric:hover {
  @apply bg-base-200/50;
}

.metric-value {
  @apply text-lg font-bold;
}

.metric-trend-up {
  @apply text-success;
}

.metric-trend-down {
  @apply text-error;
}

.metric-trend-stable {
  @apply text-info;
}

/* 响应式游戏管理布局 */
@media (max-width: 1024px) {
  .game-management-sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .game-management-layout {
    @apply flex-col;
  }

  .game-management-sidebar {
    width: 100%;
    min-height: auto;
    max-height: 60vh;
    overflow-y: auto;
  }

  .game-sidebar-item {
    @apply p-2;
  }

  .game-card {
    @apply mx-2;
  }
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-base-300 {
  scrollbar-color: oklch(var(--color-base-300)) transparent;
}

.scrollbar-track-base-100 {
  scrollbar-color: oklch(var(--color-base-300)) oklch(var(--color-base-100));
}

/* Webkit 浏览器滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: oklch(var(--color-base-100));
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: oklch(var(--color-base-300));
  border-radius: 4px;
  border: 1px solid oklch(var(--color-base-100));
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--color-base-content) / 0.3);
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background: oklch(var(--color-base-100));
}

/* 积分流水统一样式 */

/* 积分流水模态框样式 */
.points-history-modal-box {
  width: 95vw;
  max-width: 1400px;
  max-height: 95vh;
  min-height: 600px;
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
  z-index: 10000;
}

/* 模态框背景遮罩 */
.modal-backdrop {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 积分流水按钮样式 */
.points-history-btn {
  transition: all 0.2s ease;
}

.points-history-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 积分流水表格容器 */
.points-history-table-container {
  min-height: 400px;
  max-height: calc(95vh - 300px);
}

/* 积分流水表格样式 */
.points-history-table {
  font-size: 0.9rem;
  position: relative;
  min-height: 100%;
}

.points-history-table th {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 1rem 0.75rem;
  white-space: nowrap;
  box-shadow: 0 2px 4px oklch(var(--color-base-content) / 0.1);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.points-history-table td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
}

/* 积分流水滚动区域 */
.points-history-scroll {
  min-height: 400px;
  max-height: calc(95vh - 300px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.points-history-scroll::-webkit-scrollbar {
  width: 6px;
}

.points-history-scroll::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.points-history-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.points-history-scroll::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 统计卡片样式 */
.stat-item {
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .points-history-modal-box {
    width: 98vw;
    max-width: none;
    max-height: 98vh;
    min-height: 500px;
    margin: 1vh auto;
  }

  .points-history-table {
    font-size: 0.8rem;
  }

  .points-history-table th,
  .points-history-table td {
    padding: 0.5rem 0.25rem;
  }

  .points-history-table-container {
    min-height: 300px;
    max-height: calc(98vh - 250px);
  }

  .points-history-scroll {
    min-height: 300px;
    max-height: calc(98vh - 250px);
  }
}

@media (min-width: 1200px) {
  .points-history-modal-box {
    width: 90vw;
    max-width: 1600px;
  }
}

/* 滚动指示器动画 */
@keyframes scroll-hint {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(2px);
  }
}

.scroll-indicator {
  animation: scroll-hint 2s ease-in-out infinite;
}

/* JSON编辑器样式 */
.json-editor-container {
  @apply w-full;
}

.json-editor-container .field-row {
  @apply transition-all duration-200 ease-in-out;
}

.json-editor-container .field-row:hover {
  @apply bg-blue-50 border-blue-200;
}

.json-editor-container .add-field-btn {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200;
}

.json-editor-container .remove-field-btn {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200;
}

.json-editor-container .switch-mode-btn {
  @apply inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.json-editor-container .form-input {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.json-editor-container .form-select {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.json-editor-container .json-preview {
  @apply w-full px-3 py-2 text-sm font-mono bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none;
}

.json-editor-container .error-message {
  @apply text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2 mt-2;
}

/* JSON编辑器动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.json-editor-container .field-row {
  animation: slideIn 0.3s ease-out;
}

.json-editor-container .mode-switch {
  @apply transition-all duration-300 ease-in-out;
}

.json-editor-container .json-text-mode {
  animation: slideIn 0.3s ease-out;
}

.json-editor-container .form-edit-mode {
  animation: slideIn 0.3s ease-out;
}