const plugin = require("tailwindcss/plugin")
const fs = require("fs")
const path = require("path")

module.exports = plugin(function({matchComponents, theme, addUtilities, addComponents}) {
  let iconsDir = path.join(__dirname, "../../deps/heroicons/optimized")
  let values = {}
  let allIconClasses = {}
  let icons = [
    ["", "/24/outline"],
    ["-solid", "/24/solid"],
    ["-mini", "/20/solid"],
    ["-micro", "/16/solid"]
  ]

  icons.forEach(([suffix, dir]) => {
    try {
      const dirPath = path.join(iconsDir, dir)
      if (fs.existsSync(dirPath)) {
        fs.readdirSync(dirPath).forEach(file => {
          if (file.endsWith('.svg')) {
            let name = path.basename(file, ".svg") + suffix
            values[name] = {name, fullPath: path.join(dirPath, file)}

            // 预生成所有图标的 CSS 类
            try {
              const fullIconPath = path.join(dirPath, file)
              if (fs.existsSync(fullIconPath)) {
                let content = fs.readFileSync(fullIconPath).toString().replace(/\r?\n|\r/g, "")
                content = encodeURIComponent(content)
                let size = theme("spacing.6")
                if (name.endsWith("-mini")) {
                  size = theme("spacing.5")
                } else if (name.endsWith("-micro")) {
                  size = theme("spacing.4")
                }

                // 避免重复定义
                if (!allIconClasses[`.hero-${name}`]) {
                  allIconClasses[`.hero-${name}`] = {
                    [`--hero-${name}`]: `url('data:image/svg+xml;utf8,${content}')`,
                    "-webkit-mask": `var(--hero-${name})`,
                    "mask": `var(--hero-${name})`,
                    "mask-repeat": "no-repeat",
                    "background-color": "currentColor",
                    "vertical-align": "middle",
                    "display": "inline-block",
                    "width": size,
                    "height": size
                  }
                }
              }
            } catch (error) {
              console.error(`Error processing heroicon ${name}:`, error)
            }
          }
        })
      } else {
        console.warn(`Heroicons directory not found: ${dirPath}`)
      }
    } catch (error) {
      console.error(`Error reading heroicons directory ${dir}:`, error)
    }
  })

  // 添加所有图标类到 CSS 中 - 使用 addComponents 确保它们被包含
  addComponents(allIconClasses)
  addUtilities(allIconClasses)
  // 保留 matchComponents 用于动态生成（如果需要）
  matchComponents({
    "hero": ({name, fullPath}) => {
      try {
        if (!fs.existsSync(fullPath)) {
          console.warn(`Heroicon file not found: ${fullPath}`)
          return {}
        }

        let content = fs.readFileSync(fullPath).toString().replace(/\r?\n|\r/g, "")
        content = encodeURIComponent(content)
        let size = theme("spacing.6")
        if (name.endsWith("-mini")) {
          size = theme("spacing.5")
        } else if (name.endsWith("-micro")) {
          size = theme("spacing.4")
        }
        return {
          [`--hero-${name}`]: `url('data:image/svg+xml;utf8,${content}')`,
          "-webkit-mask": `var(--hero-${name})`,
          "mask": `var(--hero-${name})`,
          "mask-repeat": "no-repeat",
          "background-color": "currentColor",
          "vertical-align": "middle",
          "display": "inline-block",
          "width": size,
          "height": size
        }
      } catch (error) {
        console.error(`Error processing heroicon ${name}:`, error)
        return {}
      }
    }
  }, {values})

  // 输出统计信息
  console.log(`🎨 Heroicons: Generated ${Object.keys(allIconClasses).length} icon classes`)
  console.log(`📊 Icon variants: outline, solid, mini, micro`)
})
