
defmodule Cypridina.Teen.GameSystem.Games.Crash.CrashRoom do
  @moduledoc """
  Crash游戏房间实现

  ## 游戏流程
  1. **等待阶段** - 房间等待玩家加入
  2. **下注阶段** - 玩家下注
  3. **飞行阶段** - 倍数上升，玩家可以下车
  4. **结算阶段** - 计算输赢，准备下一轮

  ## 游戏规则
  - Crash是一个百人场游戏
  - 玩家在下注阶段下注
  - 飞行阶段倍数不断上升，玩家可以随时下车获得收益
  - 游戏会在随机时刻爆炸，未下车的玩家失去下注金额
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :crash

  alias Cypridina.Teen.GameSystem.Games.Crash.{
    CrashGame,
    CrashLogic,
    CrashMessageBuilder,
    CrashAI
  }

  require Logger



  # ==================== RoomBehaviour 回调函数实现 ====================

  @doc """
  获取最小玩家数 - Crash是百人场游戏，最小1人即可开始
  """
  @impl true
  def min_players, do: 1

  @doc """
  获取最大玩家数 - Crash是百人场游戏，支持大量玩家
  """
  @impl true
  def max_players, do: 100

  @doc """
  判断游戏是否结束 - Crash是持续运行的百人场游戏，不会结束
  """
  @impl true
  def game_over?(_state), do: false

  @doc """
  获取游戏胜者 - Crash每局都有胜负，但不是传统意义的玩家对战
  """
  @impl true
  def get_winner(state) do
    case state.game_data.phase do
      :settling ->
        # 在结算阶段，可以返回本局的爆炸倍数
        case Map.get(state.game_data, :crash_multiplier) do
          nil -> :none
          multiplier -> {:ok, "#{multiplier / 100}x"}
        end

      _ ->
        :none
    end
  end

  # ==================== 1. 初始化阶段 ====================

  @doc """
  初始化游戏逻辑
  """
  @impl true
  def init_game_logic(state) do
    unified_config = CrashGame.full_config(state.config)

    state
    |> build_initial_game_state(unified_config)
    |> start_game_loop()
  end

  @doc """
  开始游戏循环
  """
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [CRASH_GAME_START] 开始新一轮游戏 - 房间: #{state.id}, 轮次: #{state.game_data.round + 1}")

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :waiting,
        round: state.game_data.round + 1,
        total_bets: 0,
        betting_players: [],
        crash_time: nil,
        crash_multiplier: nil,
        crashed: false,
        start_time: System.system_time(:millisecond),
        phase_timer: nil
    }

    # 重置玩家状态
    reset_players =
      state.players
      |> Enum.map(fn {numeric_id, player} ->
        reset_player =
          Map.merge(player, %{
            bet_amount: 0,
            cashed_out: false,
            cash_out_multiplier: nil,
            crashed: false,
            payout: 0,
            profit: 0
          })

        {numeric_id, reset_player}
      end)
      |> Enum.into(%{})

    # 启动游戏tick
    schedule_game_tick()

    updated_state = %{state | game_data: game_data, players: reset_players}

    # 启动等待阶段
    updated_state
    |> start_waiting_phase()
  end

  # ==================== 2. 玩家管理 ====================

  @doc """
  玩家加入房间
  """
  @impl true
  def on_player_joined(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_JOIN] 玩家加入百人场 - ID: #{player.numeric_id}")

    state
    |> add_player_to_state(player)
    |> send_initial_data_to_player(player)
    |> broadcast_player_count_change()
    |> check_game_start()
  end

  @doc """
  玩家离开房间
  """
  @impl true
  def on_player_left(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_LEAVE] 玩家离开百人场 - ID: #{player.numeric_id}")

    state
    |> remove_player_from_state(player)
    |> broadcast_player_count_change()
  end

  @doc """
  玩家重连
  """
  @impl true
  def on_player_rejoined(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_REJOIN] 玩家重连 - ID: #{player.numeric_id}")

    # 发送完整的重连数据，不影响游戏流程
    send_complete_reconnect_data(state, player)

    state
  end

  # ==================== 3. 消息处理 ====================

  @doc """
  处理游戏消息
  """
  @impl true
  def handle_game_message(state, player, message) do
    client_protocols = CrashMessageBuilder.client_protocols()

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.place_bet ->
        # 玩家下注
        handle_place_bet(state, player, message)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.cash_out ->
        # 玩家下车
        handle_cash_out(state, player, message)

      _ ->
        Logger.info("🎰 [CRASH_UNKNOWN_MESSAGE] 未知消息 - #{inspect(message)}")
        state
    end
  end

  # 处理退出房间协议
  defp handle_exit_room_protocol(state, player, _data) do
    Logger.info("🎰 [CRASH_EXIT_ROOM] 玩家请求退出房间 - ID: #{player.numeric_id}")

    # 移除玩家
    new_state = remove_player_from_state(state, player)

    # 广播玩家离开
    leave_message = %{
      "mainId" => 4,
      "subId" => 14,
      "data" => %{
        "player_id" => player.numeric_id
      }
    }

    broadcast_to_all_except(new_state, leave_message, player.numeric_id)

    new_state
  end

  # ==================== 4. 游戏阶段处理 ====================

  # ==================== 私有函数 ====================

  defp build_initial_game_state(state, config) do
    game_data = %{
      phase: :waiting,
      round: 0,
      config: config,
      total_bets: 0,
      betting_players: [],
      crash_time: nil,
      crash_multiplier: nil,
      crashed: false,
      start_time: nil,
      betting_time_left: Map.get(config, :betting_time, 15),
      flying_start_time: nil,
      multiplier_history: [],
      # 添加阶段定时器管理
      phase_timer: nil
    }

    %{state | game_data: game_data}
  end

  defp start_game_loop(state) do
    # 百人场游戏自动开始，无需等待玩家数量
    Logger.info("🎰 [CRASH_GAME_LOOP] 启动游戏循环")

    # 直接开始第一轮游戏
    on_game_start(state)
  end

  defp start_waiting_phase(state) do
    config = state.game_data.config
    free_time = Map.get(config, :free_time, 5)
    current_time = System.system_time(:millisecond)

    Logger.info("🎰 [CRASH_WAITING_PHASE] 开始等待阶段 - 时间: #{free_time}秒")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, free_time * 1000)

    # 更新游戏状态
    updated_game_data = Map.merge(state.game_data, %{
      phase: :waiting,
      waiting_start_time: current_time,  # 记录等待开始时间
      phase_timer: timer
    })
    updated_state = %{state | game_data: updated_game_data}

    # 发送游戏空闲消息
    message = CrashMessageBuilder.build_game_free(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_betting_phase(state) do
    config = state.game_data.config
    betting_time = Map.get(config, :betting_time, 15)

    Logger.info("🎰 [CRASH_BETTING_PHASE] 开始下注阶段 - 时间: #{betting_time}秒")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, betting_time * 1000)

    # 更新游戏状态
    current_time = System.system_time(:millisecond)
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :betting,
        betting_time_left: betting_time,
        betting_start_time: current_time,  # 记录下注开始时间
        phase_timer: timer
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送游戏开始消息
    message = CrashMessageBuilder.build_game_start(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_flying_phase(state) do
    Logger.info("🚀 [CRASH_FLYING_PHASE] 开始飞行阶段")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 获取下注玩家信息
    betting_players = get_betting_players(state)

    # 计算爆炸点（倍数和时间）
    {crash_multiplier, crash_time} = CrashLogic.calculate_crash_point(state, betting_players)

    # 更新游戏状态（飞行阶段不设置固定定时器，由爆炸逻辑控制）
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :flying,
        flying_start_time: System.system_time(:millisecond),
        crash_time: crash_time,
        crash_multiplier: crash_multiplier,
        phase_timer: nil
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送飞行开始消息
    fly_message = CrashMessageBuilder.build_fly_start(updated_state)
    broadcast_to_all(updated_state, fly_message)

    # 启动游戏tick来处理飞行阶段
    schedule_game_tick()

    updated_state
  end

  defp handle_betting_tick(state) do
    # 处理机器人下注行为
    state_with_robot_bets = CrashAI.handle_robot_betting_phase(state)

    # 更新下注倒计时
    time_left = Map.get(state_with_robot_bets.game_data, :betting_time_left, 0) - 1

    if time_left <= 0 do
      # 下注时间结束，进入飞行阶段
      start_flying_phase(state_with_robot_bets)
    else
      updated_game_data = Map.put(state_with_robot_bets.game_data, :betting_time_left, time_left)
      %{state_with_robot_bets | game_data: updated_game_data}
    end
  end

  defp handle_flying_tick(state) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    # 检查是否应该爆炸
    if CrashLogic.should_crash?(state, elapsed_time) do
      handle_crash(state, elapsed_time)
    else
      # 获取当前倍数
      config = state.game_data.config
      current_multiplier = CrashLogic.get_current_multiplier(elapsed_time, config)

      # 处理机器人下车行为
      state_with_robot_cashouts =
        CrashAI.handle_robot_flying_phase(state, current_multiplier, elapsed_time)

      # 只在倍数有显著变化时才发送更新（减少协议发送频率）
      last_sent_multiplier = Map.get(state.game_data, :last_sent_multiplier, 100)
      multiplier_diff = abs(current_multiplier - last_sent_multiplier)

      # 每当倍数变化超过5个点（0.05x）或每秒发送一次更新
      last_update_time = Map.get(state.game_data, :last_update_time, 0)
      should_send_update = multiplier_diff >= 5 or (current_time - last_update_time) >= 500

      updated_state = if should_send_update do
        # 发送倍数更新消息（使用时间通知协议而不是飞行开始协议）
        time_data = %{
          "multiplier" => current_multiplier / 100.0,
          "time" => elapsed_time / 1000.0,
          "timestamp" => current_time
        }

        # 使用时间通知协议（1008）发送倍数更新
        message = CrashMessageBuilder.build_message(
          CrashMessageBuilder.protocol_ids().time_notify,
          time_data
        )

        broadcast_to_all(state_with_robot_cashouts, message)

        # 更新最后发送的倍数和时间
        updated_game_data = Map.merge(state_with_robot_cashouts.game_data, %{
          last_sent_multiplier: current_multiplier,
          last_update_time: current_time
        })
        %{state_with_robot_cashouts | game_data: updated_game_data}
      else
        state_with_robot_cashouts
      end

      updated_state
    end
  end

  defp handle_settling_tick(state) do
    # 结算阶段处理 - 由定时器控制，这里不需要额外处理
    state
  end

  defp handle_crash(state, crash_time) do
    Logger.info("💥 [CRASH_EXPLODE] 游戏爆炸 - 爆炸时间: #{crash_time}ms")

    # 处理爆炸逻辑
    {:ok, updated_state} = CrashLogic.handle_crash(state, crash_time)

    # 修复玩家结果数据格式
    player_results =
      Enum.map(updated_state.players, fn {numeric_id, player} ->
        # 直接从玩家数据中获取金币，避免使用PlayerData.get_points
        player_coin =
          if player.is_robot do
            # 机器人使用balance字段
            Map.get(player, :balance, 1000000)
          else
            # 真实玩家从账户系统获取
            Cypridina.Accounts.get_user_points(player.user_id)
          end

        %{
          "playerid" => player.numeric_id,  # 修正字段名
          "bet_amount" => Map.get(player, :bet_amount, 0),
          "cash_out_multiplier" => Map.get(player, :cash_out_multiplier, nil),
          "win_amount" => Map.get(player, :payout, 0),  # 修正字段名
          "coin" => player_coin,  # 直接使用获取的金币值
          "is_crashed" => not Map.get(player, :cashed_out, false)  # 修正字段名和逻辑
        }
      end)

    # 发送游戏结束消息
    crash_multiplier = Map.get(updated_state.game_data, :crash_multiplier, 100)
    config = updated_state.game_data.config
    settling_time = Map.get(config, :settling_time, 5)

    # 使用正确的参数调用build_game_end
    message = CrashMessageBuilder.build_game_end(crash_time, crash_multiplier, settling_time)
    broadcast_to_all(updated_state, message)

    # 记录历史
    updated_game_data =
      Map.update(updated_state.game_data, :multiplier_history, [], fn history ->
        new_record = %{
          round_id: Map.get(updated_state.game_data, :round, 1),
          crash_multiplier: crash_multiplier,
          timestamp: System.system_time(:millisecond),
          player_count: map_size(updated_state.players)
        }
        # 保留最近20条
        [new_record | history] |> Enum.take(20)
      end)

    # 转换到结算阶段
    transition_to_settling(%{updated_state | game_data: updated_game_data})
  end

  defp transition_to_settling(state) do
    config = state.game_data.config
    settling_time = Map.get(config, :settling_time, 5)

    Logger.info("🎰 [CRASH_SETTLING_PHASE] 开始结算阶段 - 时间: #{settling_time}秒")

    # 设置结算阶段定时器
    timer = Process.send_after(self(), :phase_timeout, settling_time * 1000)

    # 更新游戏状态
    current_time = System.system_time(:millisecond)
    updated_game_data = Map.merge(state.game_data, %{
      phase: :settling,
      settling_start_time: current_time,  # 记录结算开始时间
      phase_timer: timer
    })
    updated_state = %{state | game_data: updated_game_data}

    # 发送结算状态消息
    message = CrashMessageBuilder.build_game_state(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  # ==================== 5. 消息处理函数 ====================

  defp handle_place_bet(state, player, message) do
    # 前端发送的字段是 lBetScore，不是 betAmount
    bet_amount = get_in(message, ["data", "lBetScore"]) || 0

    case CrashLogic.place_bet(state, player, bet_amount) do
      {:ok, updated_state, updated_player} ->
        Logger.info("💰 [CRASH_PLACE_BET] 玩家下注成功 - ID: #{player.numeric_id}, 下注金额: #{bet_amount}")

        # 先扣除玩家积分，然后获取扣除后的金币数量
        final_state = subtract_player_points(updated_state, updated_player.numeric_id, bet_amount)
        current_money = get_player_points(final_state, updated_player.numeric_id)
        total_bets = Map.get(final_state.game_data, :total_bets, 0)

        # 发送下注成功消息给玩家
        success_message =
          CrashMessageBuilder.build_place_bet_success(
            updated_player,
            bet_amount,
            current_money,
            total_bets
          )

        send_to_player(final_state, updated_player, success_message)

        # 发送下注成功消息给其他玩家（实现下注同步）
        sync_message =
          CrashMessageBuilder.build_place_bet_success(
            updated_player,
            bet_amount,
            current_money,
            final_state.game_data.total_bets
          )

        broadcast_to_all_except(final_state, sync_message, updated_player.numeric_id)

        final_state

      {:error, error_code} ->
        Logger.warning("❌ [CRASH_PLACE_BET_FAIL] 玩家下注失败 - ID: #{player.numeric_id}, 下注金额: #{bet_amount}, 错误: #{error_code}")

        # 发送下注失败消息
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  @doc """
  计算当前游戏轮次的统计信息
  """
  defp calculate_game_stats(state) do
    players = Map.values(state.players)

    # 计算下注总余额（所有玩家的下注金额总和）
    total_bet_amount =
      players
      |> Enum.map(&Map.get(&1, :bet_amount, 0))
      |> Enum.sum()

    # 计算下车总余额（所有已下车玩家的获得金额总和）
    total_cash_out_amount =
      players
      |> Enum.filter(&Map.get(&1, :cashed_out, false))
      |> Enum.map(&Map.get(&1, :payout, 0))
      |> Enum.sum()

    # 计算上车总人数（有下注的玩家数量）
    total_bet_players =
      players
      |> Enum.count(&(Map.get(&1, :bet_amount, 0) > 0))

    # 计算下车总人数（已下车的玩家数量）
    total_cash_out_players =
      players
      |> Enum.count(&Map.get(&1, :cashed_out, false))

    %{
      total_bet_amount: total_bet_amount,
      total_cash_out_amount: total_cash_out_amount,
      total_bet_players: total_bet_players,
      total_cash_out_players: total_cash_out_players
    }
  end

  defp handle_cash_out(state, player, _message) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    case CrashLogic.cash_out(state, player, elapsed_time) do
      {:ok, updated_state, updated_player, payout_info} ->
        Logger.info("🚗 [CRASH_CASH_OUT] 玩家下车成功 - ID: #{player.numeric_id}, 下车时间: #{elapsed_time}ms, 下车倍数: #{updated_player.cash_out_multiplier / 100}x, 下车金额: #{payout_info.gross_payout}, 净收益: #{payout_info.net_payout}, 利润: #{payout_info.profit}")

        # 先增加玩家积分，然后获取更新后的金币数量
        final_state = add_player_points(updated_state, updated_player.numeric_id, payout_info.net_payout)
        current_money = get_player_points(final_state, updated_player.numeric_id)

        # 计算游戏统计信息
        game_stats = calculate_game_stats(final_state)

        # 发送下车成功消息
        cash_out_message =
          CrashMessageBuilder.build_cash_out_success(
            updated_player,
            elapsed_time,  # 下车时间（相对于飞行开始的毫秒数）
            updated_player.cash_out_multiplier,  # 下车倍数（实际倍数，如2.5）
            payout_info.gross_payout,  # 下车所得金额
            current_money,  # 玩家剩余金币（已包含奖励）
            game_stats  # 游戏统计信息
          )

        broadcast_to_all(final_state, cash_out_message)

        final_state

      {:error, error_code} ->
        Logger.warning("❌ [CRASH_CASH_OUT_FAIL] 玩家下车失败 - ID: #{player.numeric_id}, 错误: #{error_code}, 当前时间: #{elapsed_time}ms")

        # 发送下车失败消息（使用下注失败协议，错误消息不同）
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  # ==================== 6. 辅助函数 ====================

  defp add_player_to_state(state, player) do
    # 初始化玩家游戏数据
    game_player =
      Map.merge(player, %{
        bet_amount: 0,
        cashed_out: false,
        cash_out_multiplier: nil,
        crashed: false,
        payout: 0,
        profit: 0
      })

    # 使用 numeric_id 作为键，与 RoomBase 保持一致
    %{state | players: Map.put(state.players, player.numeric_id, game_player)}
  end

  defp remove_player_from_state(state, player) do
    # 使用 numeric_id 作为键，与 RoomBase 保持一致
    %{state | players: Map.delete(state.players, player.numeric_id)}
  end

  defp send_initial_data_to_player(state, player) do
    player_id = player.numeric_id

    # 1. 发送房间信息协议 (mainId=4, subId=2) - 必须发送
    send_room_info_protocol(state, player_id)

    # 2. 广播玩家加入 (mainId=4, subId=12) - 必须发送，在协议2后面
    broadcast_player_join_protocol(state, player)

    # 3. 发送游戏配置
    config_message = CrashMessageBuilder.build_game_config(state)
    send_to_player(state, player, config_message)

    # 4. 发送游戏状态
    state_message = CrashMessageBuilder.build_game_state(state)
    send_to_player(state, player, state_message)

    # 5. 发送游戏记录（包含历史记录）
    history = Map.get(state.game_data, :multiplier_history, [])

    if length(history) > 0 do
      history_message = CrashMessageBuilder.build_game_record(history)
      send_to_player(state, player, history_message)
    end

    # 6. 发送断线重连协议 (mainId=5, subId=0) - 必须发送
    send_reconnect_data_to_player(state, player)

    state
  end

  defp broadcast_player_count_change(state) do
    total_players = map_size(state.players)
    Logger.info("🎰 [CRASH_BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 总数: #{total_players}")

    message = CrashMessageBuilder.build_player_count_change(total_players)
    broadcast_to_room(state, message)

    state
  end

  defp check_game_start(state) do
    player_count = map_size(state.players)
    min_players = Map.get(state.game_data.config, :min_players, 1)

    # 如果玩家不足，添加机器人
    state_with_robots =
      if player_count < 5 do
        CrashAI.add_robots_to_room(state, 5 - player_count)
      else
        state
      end

    updated_player_count = map_size(state_with_robots.players)

    if state_with_robots.room_state == @room_states.waiting and
         updated_player_count >= min_players do
      Logger.info("🎰 [CRASH_ROOM] 房间满足开始条件 - 房间: #{state_with_robots.id}, 玩家数: #{updated_player_count}")
      start_game(state_with_robots)
    else
      state_with_robots
    end
  end

  defp broadcast_to_all(state, message) do
    Enum.each(state.players, fn {_numeric_id, player} ->
      send_to_player(state, player, message)
    end)
  end

  defp broadcast_to_all_except(state, message, exclude_numeric_id) do
    Enum.each(state.players, fn {numeric_id, player} ->
      if numeric_id != exclude_numeric_id do
        send_to_player(state, player, message)
      end
    end)
  end

  # 注意：send_to_player 函数已经由 RoomBase 提供

  defp schedule_game_tick() do
    # 每100ms一次tick
    Process.send_after(self(), :game_tick, 100)
  end

  defp get_betting_players(state) do
    state.players
    |> Enum.filter(fn {_user_id, player} ->
      Map.get(player, :bet_amount, 0) > 0
    end)
    |> Enum.map(fn {_user_id, player} -> player end)
  end

  # ==================== 7. GenServer消息处理 ====================

  # 处理阶段超时
  def handle_info(:phase_timeout, state) do
    Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 阶段超时触发 - 阶段: #{state.game_data.phase}, 轮次: #{state.game_data.round}")

    new_state =
      case state.game_data.phase do
        :waiting ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 等待阶段超时，转换到下注阶段")
          start_betting_phase(state)

        :betting ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 下注阶段超时，转换到飞行阶段")
          start_flying_phase(state)

        :settling ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 结算阶段超时，开始新一轮")
          start_new_round(state)

        _ ->
          Logger.warning("🎰 [CRASH_PHASE_TIMEOUT] 未知阶段超时 - 阶段: #{state.game_data.phase}")
          state
      end

    {:noreply, new_state}
  end

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    Logger.info("🎰 [CRASH_START_NEW_ROUND] 开始新一轮游戏")
    new_state = start_new_round(state)
    {:noreply, new_state}
  end

  # 处理游戏tick
  def handle_info(:game_tick, state) do
    # 处理当前阶段的tick逻辑
    new_state = handle_game_tick(state)
    {:noreply, new_state}
  end

  # 游戏tick处理逻辑
  @impl true
  def handle_game_tick(state) do
    # 处理当前阶段的tick
    new_state = case state.game_data.phase do
      :betting ->
        handle_betting_tick(state)

      :flying ->
        handle_flying_tick(state)

      :settling ->
        handle_settling_tick(state)

      _ ->
        state
    end

    # 继续调度下一次tick（只有在飞行阶段需要持续tick）
    if new_state.game_data.phase == :flying do
      schedule_game_tick()
    end

    new_state
  end

  # 开始新一轮游戏
  defp start_new_round(state) do
    # 百人场游戏自动循环，直接开始新一轮
    on_game_start(state)
  end

  # ==================== 8. 协议处理函数 ====================

  # 发送房间信息协议 (mainId=4, subId=2)
  defp send_room_info_protocol(state, player_id) do
    room_info = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => %{
        "room_id" => state.id,
        "game_id" => state.game_id,
        "server_id" => state.config[:server_id] || 2301,
        "max_players" => state.max_players,
        "current_players" => map_size(state.players),
        "room_state" => state.room_state,
        "game_state" => Map.get(state.game_data, :phase, :waiting),
        "round" => Map.get(state.game_data, :round, 0)
      }
    }

    case Map.get(state.players, player_id) do
      nil ->
        Logger.error("🎰 [CRASH_ROOM_INFO] 玩家不存在 - ID: #{player_id}")
      player ->
        send_to_player(state, player, room_info)
        Logger.info("🎰 [CRASH_ROOM_INFO] 发送房间信息 - ID: #{player_id}")
    end
  end

  # 发送完整的重连数据给玩家
  defp send_complete_reconnect_data(state, player) do
    player_id = player.numeric_id

    # 1. 发送房间信息协议 (mainId=4, subId=2)
    send_room_info_protocol(state, player_id)

    # 2. 发送游戏配置协议 (mainId=5, subId=1002)
    config_message = CrashMessageBuilder.build_game_config(state)
    send_to_player(state, player, config_message)

    # 3. 发送当前游戏状态协议 (mainId=5, subId=1001)
    state_message = CrashMessageBuilder.build_game_state(state)
    send_to_player(state, player, state_message)

    # 4. 发送游戏记录协议 (mainId=5, subId=1003)
    history = Map.get(state.game_data, :multiplier_history, [])
    if length(history) > 0 do
      history_message = CrashMessageBuilder.build_game_record(history)
      send_to_player(state, player, history_message)
    end

    # 5. 发送断线重连协议 (mainId=5, subId=0) - 包含完整状态信息和时间戳
    current_time = System.system_time(:millisecond)
    reconnect_data = %{
      "mainId" => 5,
      "subId" => 0,
      "data" => %{
        "room_id" => state.id,
        "player_id" => player_id,
        "game_state" => Map.get(state.game_data, :phase, :waiting),
        "round" => Map.get(state.game_data, :round, 0),
        "time_left" => get_time_left(state),
        "current_multiplier" => get_current_multiplier(state),
        "total_players" => map_size(state.players),
        "total_bets" => get_total_bets(state),
        "player_bet" => get_player_bet(state, player_id),
        "timestamp" => current_time,
        # 当前阶段开始时间戳
        "phase_start_time" => get_current_phase_start_time(state),
        # 添加配置信息，前端可以据此计算总时长
        "phase_durations" => %{
          "free_time" => Map.get(state.game_data.config, :free_time, 5),
          "betting_time" => Map.get(state.game_data.config, :betting_time, 15),
          "settling_time" => Map.get(state.game_data.config, :settling_time, 5)
        }
      }
    }

    send_to_player(state, player, reconnect_data)
    Logger.info("🎰 [CRASH_RECONNECT] 发送完整重连数据 - ID: #{player_id}")

    state
  end

  # 发送重连数据给玩家（简化版本，保持向后兼容）
  defp send_reconnect_data_to_player(state, player) do
    send_complete_reconnect_data(state, player)
  end

  # 广播玩家加入协议 (mainId=4, subId=12)
  defp broadcast_player_join_protocol(state, player) do
    # 安全获取玩家积分，避免Ash.NotLoaded错误
    player_points =
      if player.is_robot do
        Map.get(player, :balance, 1000000)
      else
        # 从账户系统获取真实玩家积分
        case Cypridina.Accounts.get_user_points(player.user_id) do
          points when is_integer(points) -> points
          _ -> 0
        end
      end

    join_message = %{
      "mainId" => 4,
      "subId" => 12,
      "data" => %{
        "player_id" => player.numeric_id,
        "nickname" => get_player_nickname(player),
        "avatar" => get_player_avatar(player),
        "points" => player_points,
        "is_robot" => player.is_robot
      }
    }

    broadcast_to_all_except(state, join_message, player.numeric_id)
    Logger.info("🎰 [CRASH_PLAYER_JOIN_BROADCAST] 广播玩家加入 - ID: #{player.numeric_id}")
  end



  # 安全获取玩家昵称
  defp get_player_nickname(player) do
    if player.is_robot do
      Map.get(player, :nickname, "机器人#{player.numeric_id}")
    else
      # 从profile获取昵称，避免Ash.NotLoaded
      case Map.get(player.user, :profile) do
        %{nickname: nickname} when is_binary(nickname) -> nickname
        _ -> "玩家#{player.numeric_id}"
      end
    end
  end

  # 安全获取玩家头像
  defp get_player_avatar(player) do
    if player.is_robot do
      Map.get(player, :avatar_id, 1)
    else
      # 从profile获取头像ID，避免Ash.NotLoaded
      case Map.get(player.user, :profile) do
        %{head_id: head_id} when is_integer(head_id) -> head_id
        _ -> 1
      end
    end
  end

  # 获取当前阶段开始时间
  defp get_current_phase_start_time(state) do
    current_time = System.system_time(:millisecond)

    case state.game_data.phase do
      :waiting -> Map.get(state.game_data, :waiting_start_time, current_time)
      :betting -> Map.get(state.game_data, :betting_start_time, current_time)
      :flying -> Map.get(state.game_data, :flying_start_time, current_time)
      :settling -> Map.get(state.game_data, :settling_start_time, current_time)
      _ -> current_time
    end
  end

  # 获取剩余时间 - 基于阶段开始时间戳计算
  defp get_time_left(state) do
    current_time = System.system_time(:millisecond)
    phase_start_time = get_current_phase_start_time(state)

    case state.game_data.phase do
      :waiting ->
        total_time = Map.get(state.game_data.config, :free_time, 5) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      :betting ->
        total_time = Map.get(state.game_data.config, :betting_time, 15) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      :flying ->
        # 飞行阶段没有固定时间限制，返回已飞行时间
        div(current_time - phase_start_time, 1000)

      :settling ->
        total_time = Map.get(state.game_data.config, :settling_time, 5) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      _ -> 0
    end
  end

  # 获取当前倍数
  defp get_current_multiplier(state) do
    case state.game_data.phase do
      :flying ->
        current_time = System.system_time(:millisecond)
        flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
        elapsed_time = current_time - flying_start_time
        CrashLogic.get_current_multiplier(elapsed_time, state.game_data.config)
      _ ->
        100  # 默认1.00x
    end
  end

  # 获取总下注金额
  defp get_total_bets(state) do
    Map.get(state.game_data, :total_bets, 0)
  end

  # 获取玩家下注信息
  defp get_player_bet(state, player_id) do
    bets = Map.get(state.game_data, :player_bets, %{})
    Map.get(bets, player_id, nil)
  end

  # 注意：handle_info 函数已经由 RoomBase 提供，这里只需要实现特定的回调
end
