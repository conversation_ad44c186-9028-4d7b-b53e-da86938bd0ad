defmodule Teen.ResourceActions.ExportWithdrawalRecords do
  @moduledoc """
  导出提现记录的资源操作
  """

  use Backpex.ResourceAction

  import Ecto.Changeset
  require Logger

  @impl Backpex.ResourceAction
  def title, do: "导出提现记录"

  @impl Backpex.ResourceAction
  def label, do: "导出记录"

  @impl Backpex.ResourceAction
  def fields do
    []
  end

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    # 导出不需要额外字段
    change
  end

  @impl Backpex.ResourceAction
  def handle(socket, _data) do
    %{selected_items: selected_items} = socket.assigns

    if length(selected_items) == 0 do
      socket = 
        socket
        |> Phoenix.LiveView.put_flash(:error, "请选择要导出的记录")

      {:ok, socket}
    else
      # 生成CSV内容
      csv_content = generate_csv(selected_items)
      
      # 生成文件名
      timestamp = DateTime.utc_now() |> DateTime.to_string() |> String.replace(~r/[:\s]/, "_")
      filename = "withdrawal_records_#{timestamp}.csv"

      # 发送下载
      socket = 
        socket
        |> Phoenix.LiveView.push_event("download", %{
          filename: filename,
          content: csv_content,
          mime_type: "text/csv"
        })
        |> Phoenix.LiveView.put_flash(:info, "成功导出 #{length(selected_items)} 条记录")

      {:ok, socket}
    end
  end

  defp generate_csv(records) do
    # CSV 头部
    headers = [
      "提现订单号",
      "用户ID",
      "用户名",
      "提现金额(元)",
      "手续费(元)",
      "实际到账(元)",
      "提现方式",
      "银行信息",
      "审核状态",
      "处理状态",
      "申请时间",
      "审核时间",
      "审核人",
      "审核反馈",
      "所需流水(元)",
      "已完成流水(元)",
      "IP地址"
    ]

    # 生成数据行
    rows = 
      records
      |> Enum.map(fn record ->
        [
          record.order_id || "",
          record.user_id || "",
          get_username(record),
          format_amount(record.withdrawal_amount),
          format_amount(record.fee_amount),
          format_amount(record.actual_amount),
          payment_method_text(record.payment_method),
          format_bank_info(record),
          audit_status_text(record.audit_status),
          progress_status_text(record.progress_status),
          format_datetime(record.inserted_at),
          format_datetime(record.audit_time),
          get_auditor_name(record),
          record.feedback || "",
          format_amount(record.required_turnover),
          format_amount(record.completed_turnover),
          record.ip_address || ""
        ]
      end)

    # 组合成CSV
    csv_data = [headers | rows]
    
    csv_data
    |> Enum.map(fn row ->
      row
      |> Enum.map(&escape_csv_field/1)
      |> Enum.join(",")
    end)
    |> Enum.join("\n")
  end

  defp escape_csv_field(field) when is_nil(field), do: ""
  defp escape_csv_field(field) do
    field_str = to_string(field)
    if String.contains?(field_str, [",", "\"", "\n"]) do
      "\"#{String.replace(field_str, "\"", "\"\"")}\""
    else
      field_str
    end
  end

  defp get_username(record) do
    case record.user do
      %{username: username} -> username
      _ -> "未知用户"
    end
  end

  defp get_auditor_name(record) do
    case record.auditor do
      %{username: username} -> username
      _ -> ""
    end
  end

  defp format_amount(nil), do: "0.00"
  defp format_amount(amount) do
    Decimal.div(amount, 100) |> Decimal.to_string()
  end

  defp format_datetime(nil), do: ""
  defp format_datetime(datetime) do
    datetime
    |> DateTime.shift_zone!("Asia/Shanghai")
    |> DateTime.to_string()
  end

  defp format_bank_info(record) do
    info_parts = []
    
    # 根据支付方式提取信息
    case record.payment_method do
      "bank_card" ->
        case Jason.decode(record.bank_info || "{}") do
          {:ok, data} ->
            [
              Map.get(data, "bank_name", ""),
              Map.get(data, "card_number", ""),
              Map.get(data, "account_name", "")
            ]
            |> Enum.reject(&(&1 == ""))
            |> Enum.join(" | ")
          _ -> ""
        end
        
      "alipay" ->
        case Jason.decode(record.alipay_info || "{}") do
          {:ok, data} ->
            [
              Map.get(data, "account", ""),
              Map.get(data, "real_name", "")
            ]
            |> Enum.reject(&(&1 == ""))
            |> Enum.join(" | ")
          _ -> ""
        end
        
      "upi" ->
        case Jason.decode(record.upi_info || "{}") do
          {:ok, data} ->
            Map.get(data, "upi_id", "")
          _ -> ""
        end
        
      _ -> ""
    end
  end

  defp payment_method_text("bank_card"), do: "银行卡"
  defp payment_method_text("alipay"), do: "支付宝"
  defp payment_method_text("upi"), do: "UPI"
  defp payment_method_text(_), do: "未知"

  defp audit_status_text(0), do: "待审核"
  defp audit_status_text(1), do: "审核通过"
  defp audit_status_text(2), do: "审核拒绝"
  defp audit_status_text(_), do: "未知"

  defp progress_status_text(0), do: "排队中"
  defp progress_status_text(1), do: "处理中"
  defp progress_status_text(2), do: "支付成功"
  defp progress_status_text(3), do: "支付失败"
  defp progress_status_text(4), do: "人工处理"
  defp progress_status_text(5), do: "已取消"
  defp progress_status_text(_), do: "未知"
end