defmodule Teen.Filters.PaymentMethodSelect do
  @moduledoc """
  支付方式筛选器
  
  用于筛选不同支付方式的提现申请
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "提现方式"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择提现方式..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"银行卡", "bank_card"},
      {"支付宝", "alipay"},
      {"UPI", "upi"}
    ]
  end
end