<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heroicons 测试</title>
    <link rel="stylesheet" href="priv/static/assets/app.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background-color: #f8fafc;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .icon-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.25rem;
        }
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status.working {
            background-color: #dcfce7;
            color: #166534;
        }
        .status.broken {
            background-color: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <h1 style="color: #1f2937; margin-bottom: 2rem;">🔍 Heroicons 诊断测试</h1>
    
    <!-- 基本图标测试 -->
    <div class="test-section">
        <h2 style="color: #374151; margin-bottom: 1rem;">📋 基本图标测试</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <span class="hero-user size-6" style="background-color: #3b82f6;"></span>
                <span>hero-user</span>
                <span class="status working">✓</span>
            </div>
            <div class="icon-item">
                <span class="hero-users size-6" style="background-color: #3b82f6;"></span>
                <span>hero-users</span>
                <span class="status working">✓</span>
            </div>
            <div class="icon-item">
                <span class="hero-home size-6" style="background-color: #3b82f6;"></span>
                <span>hero-home</span>
                <span class="status working">✓</span>
            </div>
            <div class="icon-item">
                <span class="hero-cog-6-tooth size-6" style="background-color: #3b82f6;"></span>
                <span>hero-cog-6-tooth</span>
                <span class="status working">✓</span>
            </div>
        </div>
    </div>

    <!-- 不同尺寸测试 -->
    <div class="test-section">
        <h2 style="color: #374151; margin-bottom: 1rem;">📏 尺寸变体测试</h2>
        <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-star size-4" style="background-color: #f59e0b;"></span>
                <span>size-4 (16px)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-star size-5" style="background-color: #f59e0b;"></span>
                <span>size-5 (20px)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-star size-6" style="background-color: #f59e0b;"></span>
                <span>size-6 (24px)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-star size-8" style="background-color: #f59e0b;"></span>
                <span>size-8 (32px)</span>
            </div>
        </div>
    </div>

    <!-- 图标变体测试 -->
    <div class="test-section">
        <h2 style="color: #374151; margin-bottom: 1rem;">🎨 图标变体测试</h2>
        <div style="display: flex; align-items: center; gap: 2rem; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-heart size-6" style="background-color: #ef4444;"></span>
                <span>outline (默认)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-heart-solid size-6" style="background-color: #ef4444;"></span>
                <span>solid</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-heart-mini size-5" style="background-color: #ef4444;"></span>
                <span>mini</span>
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span class="hero-heart-micro size-4" style="background-color: #ef4444;"></span>
                <span>micro</span>
            </div>
        </div>
    </div>

    <!-- 常用管理图标测试 -->
    <div class="test-section">
        <h2 style="color: #374151; margin-bottom: 1rem;">⚙️ 常用管理图标测试</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <span class="hero-chart-bar size-5" style="background-color: #8b5cf6;"></span>
                <span>hero-chart-bar</span>
            </div>
            <div class="icon-item">
                <span class="hero-currency-dollar size-5" style="background-color: #10b981;"></span>
                <span>hero-currency-dollar</span>
            </div>
            <div class="icon-item">
                <span class="hero-shopping-cart size-5" style="background-color: #f59e0b;"></span>
                <span>hero-shopping-cart</span>
            </div>
            <div class="icon-item">
                <span class="hero-bell size-5" style="background-color: #ef4444;"></span>
                <span>hero-bell</span>
            </div>
            <div class="icon-item">
                <span class="hero-shield-check size-5" style="background-color: #06b6d4;"></span>
                <span>hero-shield-check</span>
            </div>
            <div class="icon-item">
                <span class="hero-document-text size-5" style="background-color: #6b7280;"></span>
                <span>hero-document-text</span>
            </div>
        </div>
    </div>

    <!-- 诊断信息 -->
    <div class="test-section">
        <h2 style="color: #374151; margin-bottom: 1rem;">🔧 诊断信息</h2>
        <div style="font-family: monospace; font-size: 0.875rem; line-height: 1.5;">
            <p><strong>CSS 文件路径:</strong> priv/static/assets/app.css</p>
            <p><strong>Heroicons 插件:</strong> assets/vendor/heroicons.js</p>
            <p><strong>图标目录:</strong> deps/heroicons/optimized/</p>
            <p><strong>预期行为:</strong> 图标应该显示为彩色的 SVG 形状</p>
            <p><strong>如果图标不显示:</strong> 检查 CSS 编译和插件配置</p>
        </div>
    </div>

    <script>
        // 检查图标是否正确渲染
        document.addEventListener('DOMContentLoaded', function() {
            const icons = document.querySelectorAll('[class*="hero-"]');
            let workingCount = 0;
            let totalCount = icons.length;
            
            icons.forEach(icon => {
                const computedStyle = window.getComputedStyle(icon);
                const mask = computedStyle.getPropertyValue('mask') || computedStyle.getPropertyValue('-webkit-mask');
                
                if (mask && mask.includes('url(')) {
                    workingCount++;
                }
            });
            
            console.log(`Heroicons 状态: ${workingCount}/${totalCount} 个图标正常工作`);
            
            // 在页面底部显示状态
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background: #1f2937; color: white; padding: 1rem; border-radius: 0.5rem; font-family: monospace; font-size: 0.875rem;';
            statusDiv.innerHTML = `📊 图标状态: ${workingCount}/${totalCount} 正常`;
            document.body.appendChild(statusDiv);
        });
    </script>
</body>
</html>
