defmodule Teen.ResourceActions.BatchRejectWithdrawals do
  @moduledoc """
  批量审核拒绝提现申请的资源操作
  """

  use Backpex.ResourceAction

  import Ecto.Changeset
  require Logger

  @impl Backpex.ResourceAction
  def title, do: "批量审核拒绝"

  @impl Backpex.ResourceAction
  def label, do: "批量审核拒绝"

  @impl Backpex.ResourceAction
  def fields do
    [
      feedback: %{
        module: Backpex.Fields.Textarea,
        label: "拒绝原因",
        type: :string,
        required: true
      }
    ]
  end

  @required_fields ~w[feedback]a

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_length(:feedback, min: 5, max: 500)
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{selected_items: selected_items, assigns: %{current_user: current_user}} = socket.assigns

    # 过滤出只有待审核状态的提现记录
    pending_withdrawals = 
      selected_items
      |> Enum.filter(fn withdrawal -> withdrawal.audit_status == 0 end)

    if length(pending_withdrawals) == 0 do
      socket = 
        socket
        |> Phoenix.LiveView.put_flash(:error, "没有待审核的提现申请")

      {:ok, socket}
    else
      # 批量审核拒绝
      results = 
        pending_withdrawals
        |> Enum.map(fn withdrawal ->
          case Teen.Services.WithdrawalService.reject_withdrawal(
            withdrawal.id, 
            current_user.id,
            data.feedback
          ) do
            {:ok, _} -> {:ok, withdrawal.id}
            {:error, reason} -> {:error, {withdrawal.id, reason}}
          end
        end)

      success_count = Enum.count(results, fn {status, _} -> status == :ok end)
      failed_count = Enum.count(results, fn {status, _} -> status == :error end)

      socket = 
        if success_count > 0 do
          message = "成功拒绝 #{success_count} 个提现申请"
          message = if failed_count > 0 do
            message <> "，#{failed_count} 个失败"
          else
            message
          end

          socket
          |> Phoenix.LiveView.put_flash(:info, message)
          |> Phoenix.LiveView.push_navigate(to: socket.assigns.index_path)
        else
          socket
          |> Phoenix.LiveView.put_flash(:error, "批量拒绝失败，请检查选中的提现记录")
        end

      # 清空选中项
      socket = Phoenix.LiveView.assign(socket, selected_items: [])

      {:ok, socket}
    end
  end
end