defmodule Teen.Live.Admin.HeroiconsTestLive do
  @moduledoc """
  Heroicons 图标测试页面
  用于诊断和验证 Heroicons 图标系统是否正常工作
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, page_title: "Heroicons 测试")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h1 class="card-title text-2xl">
            <.icon name="hero-swatch" class="size-6 text-primary" />
            Heroicons 图标系统测试
          </h1>
          <p class="text-base-content/70">
            验证 Heroicons 图标是否正常显示和工作
          </p>
        </div>
      </div>

      <!-- 基本图标测试 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-check-circle" class="size-5 text-success" />
            基本图标测试
          </h2>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div class="flex items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-user" class="size-6 text-primary" />
              <span class="text-sm">hero-user</span>
            </div>
            
            <div class="flex items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-users" class="size-6 text-secondary" />
              <span class="text-sm">hero-users</span>
            </div>
            
            <div class="flex items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-home" class="size-6 text-accent" />
              <span class="text-sm">hero-home</span>
            </div>
            
            <div class="flex items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-cog-6-tooth" class="size-6 text-neutral" />
              <span class="text-sm">hero-cog-6-tooth</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 尺寸测试 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-adjustments-horizontal" class="size-5 text-info" />
            尺寸变体测试
          </h2>
          
          <div class="flex items-center gap-6 mt-4">
            <div class="flex items-center gap-2">
              <.icon name="hero-star" class="size-4 text-warning" />
              <span class="text-sm">size-4</span>
            </div>
            
            <div class="flex items-center gap-2">
              <.icon name="hero-star" class="size-5 text-warning" />
              <span class="text-sm">size-5</span>
            </div>
            
            <div class="flex items-center gap-2">
              <.icon name="hero-star" class="size-6 text-warning" />
              <span class="text-sm">size-6</span>
            </div>
            
            <div class="flex items-center gap-2">
              <.icon name="hero-star" class="size-8 text-warning" />
              <span class="text-sm">size-8</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 常用管理图标 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-squares-2x2" class="size-5 text-primary" />
            常用管理图标
          </h2>
          
          <div class="grid grid-cols-3 md:grid-cols-6 gap-4 mt-4">
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-chart-bar" class="size-8 text-purple-500" />
              <span class="text-xs text-center">统计</span>
            </div>
            
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-currency-dollar" class="size-8 text-green-500" />
              <span class="text-xs text-center">财务</span>
            </div>
            
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-shopping-cart" class="size-8 text-orange-500" />
              <span class="text-xs text-center">商品</span>
            </div>
            
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-bell" class="size-8 text-red-500" />
              <span class="text-xs text-center">通知</span>
            </div>
            
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-shield-check" class="size-8 text-blue-500" />
              <span class="text-xs text-center">安全</span>
            </div>
            
            <div class="flex flex-col items-center gap-2 p-3 border rounded-lg">
              <.icon name="hero-document-text" class="size-8 text-gray-500" />
              <span class="text-xs text-center">文档</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-information-circle" class="size-5 text-info" />
            使用说明
          </h2>
          
          <div class="space-y-4 mt-4">
            <div class="alert alert-info">
              <.icon name="hero-light-bulb" class="size-5" />
              <div>
                <h3 class="font-bold">基本用法</h3>
                <div class="text-sm">
                  使用 <code class="bg-base-200 px-2 py-1 rounded">&lt;.icon name="hero-图标名" class="size-5" /&gt;</code>
                </div>
              </div>
            </div>
            
            <div class="alert alert-success">
              <.icon name="hero-check-circle" class="size-5" />
              <div>
                <h3 class="font-bold">可用尺寸</h3>
                <div class="text-sm">
                  size-4 (16px), size-5 (20px), size-6 (24px), size-8 (32px)
                </div>
              </div>
            </div>
            
            <div class="alert alert-warning">
              <.icon name="hero-exclamation-triangle" class="size-5" />
              <div>
                <h3 class="font-bold">注意事项</h3>
                <div class="text-sm">
                  • 图标名称必须以 "hero-" 开头<br>
                  • 使用 TailwindCSS 类控制颜色和大小<br>
                  • 如果图标不显示，检查名称是否正确
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
